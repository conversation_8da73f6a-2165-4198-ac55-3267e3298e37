
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
# ContextEngineering\Tasks\Cisco.md
# ContextEngineering\TechnicalNotes\journal-technique.md


# Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.


- 


- Je voulais vous informer aussi que j'ai aucun moyen de savoir dans quelle phase de mode on se trouve. L'animation se déroule comme prévu, par contre je suis incapable de vous dire est-ce que ça va être l'aube, est-ce que ça va être à midi, je devine. Parce que le problème c'est que là en mode développement, j'ai rien pour m'informer. Si je vois une erreur, comment je fais pour vous informer dans quel mode on se trouve ? Il serait judicieux, temporairement, en mode développement, de mettre quelque chose en place pour que je puisse vous informer s'il y a un problème à un mode très précis. Parce que là, ce n'est pas le cas. 


- Attention, le son démarre automatiquement avant même que je clique sur le bouton circulaire pour activer les slides. Le son normalement doit s'activer lorsque l'utilisateur clique sur le bouton circulaire à l'entrée des slides. Par ailleurs, c'est bizarre, vous m'avez toujours dit que les navigateurs bloquaient le son automatique quand ça démarrait tout seul. Et bien là, je vous confirme que non, c'est possible parce que là, le son démarre tout de suite. Avant même que je clique sur le bouton circulaire, le son est déjà en fonctionnement. Et par la même occasion, le bouton audio dans le menu principal, je clique dessus et apparemment la fonction n'est pas opérationnelle, ça ne coupe pas le son et bien entendu, il n'est pas fonctionnel du tout. 


- Attention, rappel, certains dossiers ne doivent pas être commités sur GitHub, comme le context engineering et certains fichiers sensibles, comme par exemple s'il y a des clés, attention, il ne faut pas qu'elles soient visibles. 


- Ensuite, il faudra peut-être penser à repositionner le soleil quand il se lève, le faire monter au zénith tout en haut à droite de l'écran, et ensuite à l'inverse, quand on arrive à la fin de la journée, le soleil doit redescendre puis se coucher. Ensuite, quand la nuit vient, c'est la lune qui prend le relais. Donc elle se lève, elle monte au-dessus du paysage, background. Ensuite, pareil, elle monte, elle part de la gauche, elle monte au zénith, et ensuite, quand le cycle est fait et que le lever de soleil vient à son tour, la lune se couche et le soleil prend le relais. Pensez bien à mettre à jour le fichier MD complet pour intervention manuelle pour le soleil et la lune pour que je puisse intervenir manuellement. 






























































































